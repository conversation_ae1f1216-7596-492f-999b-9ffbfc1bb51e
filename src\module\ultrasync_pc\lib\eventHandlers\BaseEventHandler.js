/**
 * 事件处理器基类
 * 提供通用的属性和方法供子类继承
 */
class BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager = null) {
        this.vm = vueInstance
        this.autoLoginManager = autoLoginManager
        this.mainScreenManager = mainScreenManager
        this.eventListenerManager = eventListenerManager
    }

    /**
     * 初始化事件监听器 - 子类需要实现此方法
     * @param {Object} controller MainScreen控制器
     */
    initEvents(controller) {
        throw new Error('子类必须实现 initEvents 方法')
    }

    /**
     * 获取防抖排序聊天列表方法
     */
    get debounceSortChatList() {
        return this.eventListenerManager?.debounceSortChatList
    }
}

export default BaseEventHandler
